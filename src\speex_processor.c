#include "audio_processor.h"
#include "logger.h"
#include "audio_realtime.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

/**
 * 初始化SpeexDSP处理器
 * @param processor 音频处理器
 * @return 0成功，-1失败
 */
int speex_processor_init(AudioProcessor *processor) {
    if (!processor) {
        fprintf(stderr, "Error: Invalid processor for speex_processor_init");
        return -1;
    }

    LOG_INFO(MODULE_SPEEX_PROCESSOR, "Initializing SpeexDSP processors...");

    // 初始化回声消除器
    #define TAIL_LENGTH (4800) // 100ms尾长，48kHz
    //#define TAIL_LENGTH (4800*2) // 200ms尾长，48kHz
    // 256为每次算法处理的一帧数据的大小（此时的数据格式为short），2048为尾音长度（一般为256*8）
    processor->echo_state = speex_echo_state_init(ALSA_FRAME_SIZE, TAIL_LENGTH);
    if (!processor->echo_state) {
        LOG_DEBUG(MODULE_SPEEX_PROCESSOR, "Echo init failed: frame_size=%d, tail_length=%d",
                  ALSA_FRAME_SIZE, TAIL_LENGTH);
        return -1;
    }
    int sample_rate = ALSA_SAMPLE_RATE;
    if (speex_echo_ctl(processor->echo_state, SPEEX_ECHO_SET_SAMPLING_RATE, &sample_rate) != 0) {
        LOG_DEBUG(MODULE_SPEEX_PROCESSOR, "Failed to set sampling rate: %d", sample_rate);
        return -1;
    }
    // 初始化预处理器（48kHz，NR和AGC）
    processor->preprocess_state = speex_preprocess_state_init(ALSA_FRAME_SIZE, ALSA_SAMPLE_RATE);
    if (!processor->preprocess_state) {
        fprintf(stderr, "Error: Failed to initialize Speex preprocessor");
        return -1;
    }

    // 关联回声消除器
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_ECHO_STATE, processor->echo_state);

    // 配置噪声抑制
    int denoise = 1;
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_DENOISE, &denoise);
    int noise_suppress = -15; // -25dB
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noise_suppress);

    // 配置AGC
    //int agc = 1;
    //float agc_level = 20000.0f; // 目标电平
    //float agc_max_gain = 10.0f; // 最大增益10dB

    int agc = 0;
    // 要减少回音，可以调整这些参数：
    float agc_level = 3000.0f;     // 降低目标电平（当前20000）
    float agc_max_gain = 4.0f;      // 降低最大增益（当前10dB）

    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_AGC, &agc);
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_AGC_LEVEL, &agc_level);
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_AGC_MAX_GAIN, &agc_max_gain);

    //int echo_suppress = -40;     // 抑制强度 (dB)
    //speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_ECHO_SUPPRESS, &echo_suppress);
    //int echo_suppress_active = -15;  // 有语音时也要抑制
    //speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_ECHO_SUPPRESS_ACTIVE, &echo_suppress_active);

    // 初始化VAD（16kHz）
    processor->vad_state = speex_preprocess_state_init(RT_FRAME_SIZE, SAMPLE_RATE_16K);
    if (!processor->vad_state) {
        fprintf(stderr, "Error: Failed to initialize Speex VAD state (errno=%d: %s)\n",
                errno, strerror(errno));
        LOG_DEBUG(MODULE_SPEEX_PROCESSOR, "VAD init failed: frame_size=%d, sample_rate=16000",
                  RT_FRAME_SIZE);
        speex_preprocess_state_destroy(processor->preprocess_state);
        speex_echo_state_destroy(processor->echo_state);
        processor->preprocess_state = NULL;
        processor->echo_state = NULL;
        return -1;
    }

    int vad = 1;
    speex_preprocess_ctl(processor->vad_state, SPEEX_PREPROCESS_SET_VAD, &vad);
    int vad_prob_start = 97;
    int vad_prob_continue = 90;
    speex_preprocess_ctl(processor->vad_state, SPEEX_PREPROCESS_SET_PROB_START, &vad_prob_start);
    speex_preprocess_ctl(processor->vad_state, SPEEX_PREPROCESS_SET_PROB_CONTINUE, &vad_prob_continue);

    // 初始化重采样器
    processor->resampler_state = speex_resampler_init(1, ALSA_SAMPLE_RATE, SAMPLE_RATE_16K, 10, NULL);
    if (!processor->resampler_state) {
        fprintf(stderr, "Error: Failed to initialize Speex resampler (errno=%d: %s)\n",
                errno, strerror(errno));
        LOG_DEBUG(MODULE_SPEEX_PROCESSOR, "Resampler init failed: input_rate=%d, output_rate=%d",
                  ALSA_SAMPLE_RATE,SAMPLE_RATE_16K);
        speex_preprocess_state_destroy(processor->vad_state);
        speex_preprocess_state_destroy(processor->preprocess_state);
        speex_echo_state_destroy(processor->echo_state);
        processor->vad_state = NULL;
        processor->preprocess_state = NULL;
        processor->echo_state = NULL;
        return -1;
    }


    LOG_INFO(MODULE_SPEEX_PROCESSOR, "SpeexDSP Preprocessor initialized: frame_size=%d, sample_rate=%d",
             ALSA_FRAME_SIZE, ALSA_SAMPLE_RATE);
    LOG_INFO(MODULE_SPEEX_PROCESSOR, " - Echo cancellation: ENABLED (tail_length=%d)", TAIL_LENGTH);
    LOG_INFO(MODULE_SPEEX_PROCESSOR, " - Noise suppression: ENABLED (%d dB)", noise_suppress);
    LOG_INFO(MODULE_SPEEX_PROCESSOR, " - AGC: ENABLED (level=%.0f, max_gain=%.0f dB)", agc_level, agc_max_gain);
    LOG_INFO(MODULE_SPEEX_PROCESSOR, " - VAD: ENABLED (16kHz, prob_start=%d, prob_continue=%d)",
             vad_prob_start, vad_prob_continue);
    LOG_INFO(MODULE_SPEEX_PROCESSOR, " - Resampler: 48kHz -> 16kHz");
    LOG_INFO(MODULE_SPEEX_PROCESSOR, " - Processing flow: 48kHz(Beamforming+AEC+NR+AGC) -> 16kHz(VAD)");

    return 0;
}
/**
 * 清理SpeexDSP处理器
 * @param processor 音频处理器
 */
void speex_processor_cleanup(AudioProcessor *processor) {
    if (!processor) {
        return;
    }
    
    if (processor->preprocess_state) {
        speex_preprocess_state_destroy(processor->preprocess_state);
        processor->preprocess_state = NULL;
        LOG_INFO(MODULE_SPEEX_PROCESSOR,"Speex preprocessor destroyed");
    }

    if (processor->vad_state) {
        speex_preprocess_state_destroy(processor->vad_state);
        processor->vad_state = NULL;
        LOG_INFO(MODULE_SPEEX_PROCESSOR,"Speex VAD destroyed");
    }
    
    if (processor->echo_state) {
        speex_echo_state_destroy(processor->echo_state);
        processor->echo_state = NULL;
        LOG_INFO(MODULE_SPEEX_PROCESSOR,"Speex echo canceller destroyed");
    }

    if (processor->resampler_state) {
        speex_resampler_destroy(processor->resampler_state);
        processor->resampler_state = NULL;
        LOG_INFO(MODULE_SPEEX_PROCESSOR,"Speex resampler destroyed");
    }

}

/**
 * 执行回声消除
 * @param processor 音频处理器
 * @param input 输入音频 (麦克风信号)
 * @param reference 参考信号 (扬声器信号)
 * @param output 输出音频 (回声消除后)
 * @return 0成功，-1失败
 */
int audio_echo_cancel(AudioProcessor *processor, const int16_t *input,
                     const int16_t *reference, int16_t *output) {
    if (!processor || !processor->echo_state || !input || !output) {
        fprintf(stderr, "Error: Invalid parameters for audio_echo_cancel");
        return -1;
    }

    // 如果没有参考信号，直接复制输入到输出
    if (!reference) {
        memcpy(output, input, FRAME_SIZE * sizeof(int16_t));
        return 0;
    }

    // 执行回声消除
    speex_echo_cancellation(processor->echo_state, input, reference, output);

    return 0;
}

/**
 * 执行噪声抑制和AGC
 * @param processor 音频处理器
 * @param audio 输入/输出音频缓冲区
 * @param samples 样本数
 * @return 0成功，-1失败
 */
int audio_noise_suppress(AudioProcessor *processor, int16_t *audio, int samples) {
    if (!processor || !processor->preprocess_state || !audio) {
        fprintf(stderr, "Error: Invalid parameters for audio_noise_suppress\n");
        return -1;
    }

    if (samples != ALSA_FRAME_SIZE) {
        fprintf(stderr, "Error: Invalid frame size for audio_noise_suppress: %d (expected %d)\n",
                samples, ALSA_FRAME_SIZE);
        return -1;
    }
    
    // 执行预处理 (噪声抑制 + AGC + VAD)
    int vad_result = speex_preprocess_run(processor->preprocess_state, audio);

    // 保存VAD结果供外部使用
    processor->last_vad_result = vad_result;

    // vad_result: 1表示检测到语音，0表示静音
    static int vad_counter = 0;
    static int speech_frames = 0;
    static int silence_frames = 0;

    if (vad_result) {
        speech_frames++;
    } else {
        silence_frames++;
    }

    // 每1000帧打印一次VAD统计
    vad_counter++;
    if (vad_counter % 10 == 0) {
        LOG_INFO(MODULE_SPEEX_PROCESSOR,"VAD Stats: Speech=%d, Silence=%d (%.1f%% speech)",
               speech_frames, silence_frames,
               100.0f * speech_frames / (speech_frames + silence_frames));
        speech_frames = 0;
        silence_frames = 0;
    }

    return 0;
}

/**
 * 获取当前AGC增益
 * @param processor 音频处理器
 * @return AGC增益值 (dB)
 */
float get_agc_gain(const AudioProcessor *processor) {
    if (!processor || !processor->preprocess_state) {
        return 0.0f;
    }
    
    float gain = 0.0f;
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_GET_AGC_GAIN, &gain);
    return gain;
}

/**
 * 获取当前噪声估计
 * @param processor 音频处理器
 * @return 噪声功率估计
 */
float get_noise_estimate(const AudioProcessor *processor) {
    if (!processor || !processor->preprocess_state) {
        return 0.0f;
    }
    
    float noise_power = 0.0f;
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_GET_NOISE_PSD, &noise_power);
    return noise_power;
}

/**
 * 设置噪声抑制强度
 * @param processor 音频处理器
 * @param suppress_db 抑制强度 (dB, 负值)
 * @return 0成功，-1失败
 */
int set_noise_suppression(AudioProcessor *processor, int suppress_db) {
    if (!processor || !processor->preprocess_state) {
        return -1;
    }
    
    if (suppress_db > 0) {
        suppress_db = -suppress_db;  // 确保是负值
    }
    
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, 
                        &suppress_db);
    
    LOG_INFO(MODULE_SPEEX_PROCESSOR,"Noise suppression set to %d dB", suppress_db);
    return 0;
}

/**
 * 设置AGC参数
 * @param processor 音频处理器
 * @param target_level 目标电平
 * @param max_gain 最大增益 (dB)
 * @param max_attenuation 最大衰减 (dB, 负值)
 * @return 0成功，-1失败
 */
int set_agc_parameters(AudioProcessor *processor, float target_level, 
                      float max_gain, float max_attenuation) {
    if (!processor || !processor->preprocess_state) {
        return -1;
    }
    
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_AGC_LEVEL, 
                        &target_level);
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_AGC_INCREMENT, 
                        &max_gain);
    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_AGC_DECREMENT, 
                        &max_attenuation);
    
    LOG_INFO(MODULE_SPEEX_PROCESSOR,"AGC parameters updated: target=%.0f, gain=%.0fdB, attenuation=%.0fdB",
           target_level, max_gain, max_attenuation);
    return 0;
}

/**
 * 设置AGC目标电平（简单增益调整）
 * @param processor 音频处理器
 * @param gain_multiplier 增益倍数 (1.0=无变化, 2.0=双倍音量)
 * @return 0成功，-1失败
 */
int set_agc_gain_multiplier(AudioProcessor *processor, float gain_multiplier) {
    if (!processor || !processor->preprocess_state) {
        return -1;
    }

    // 将增益倍数转换为目标电平
    // 基础电平15000，根据增益倍数调整
    float target_level = 15000.0f * gain_multiplier;

    // 限制在合理范围内
    if (target_level > 30000.0f) target_level = 30000.0f;
    if (target_level < 5000.0f) target_level = 5000.0f;

    speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_SET_AGC_LEVEL, &target_level);

    LOG_INFO(MODULE_SPEEX_PROCESSOR,"AGC gain multiplier set to %.1fx (target level: %.0f)", gain_multiplier, target_level);
    return 0;
}

/**
 * 打印SpeexDSP处理统计信息
 * @param processor 音频处理器
 */
void print_speex_stats(const AudioProcessor *processor) {
    if (!processor) {
        return;
    }
    
    LOG_INFO(MODULE_SPEEX_PROCESSOR,"=== SpeexDSP Statistics ===");
    
    if (processor->preprocess_state) {
        float agc_gain = get_agc_gain(processor);
        LOG_INFO(MODULE_SPEEX_PROCESSOR,"Current AGC gain: %.2f dB", agc_gain);
        
        // 获取其他统计信息
        int prob = 0;
        speex_preprocess_ctl(processor->preprocess_state, SPEEX_PREPROCESS_GET_PROB, &prob);
        LOG_INFO(MODULE_SPEEX_PROCESSOR,"Speech probability: %d%%", prob);
    }
    
    LOG_INFO(MODULE_SPEEX_PROCESSOR,"Frames processed: %lu", processor->frames_processed);
    LOG_INFO(MODULE_SPEEX_PROCESSOR,"=========================");
}
